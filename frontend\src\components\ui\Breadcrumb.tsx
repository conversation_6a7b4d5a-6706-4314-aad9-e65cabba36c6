import React from 'react';
import { cn } from '../../utils/cn';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
  maxItems?: number;
}

export function Breadcrumb({
  items,
  className,
  separator,
  maxItems = 5,
}: BreadcrumbProps) {
  const defaultSeparator = (
    <svg className="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  );

  // Truncate items if there are too many
  let displayItems = items;
  if (items.length > maxItems) {
    const firstItem = items[0];
    const lastItems = items.slice(-(maxItems - 2));
    displayItems = [
      firstItem,
      { label: '...', href: undefined, current: false },
      ...lastItems,
    ];
  }

  return (
    <nav className={cn('flex items-center space-x-2 text-sm', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.label === '...';

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 flex-shrink-0">
                  {separator || defaultSeparator}
                </span>
              )}
              
              {isEllipsis ? (
                <span className="text-secondary-500 dark:text-secondary-400 px-2">
                  {item.label}
                </span>
              ) : item.href && !item.current ? (
                <a
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-1 px-2 py-1 rounded-md transition-colors',
                    'text-secondary-600 dark:text-secondary-400',
                    'hover:text-primary-600 dark:hover:text-primary-400',
                    'hover:bg-secondary-100 dark:hover:bg-secondary-700'
                  )}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.label}</span>
                </a>
              ) : (
                <span
                  className={cn(
                    'flex items-center space-x-1 px-2 py-1',
                    isLast || item.current
                      ? 'text-secondary-900 dark:text-secondary-100 font-medium'
                      : 'text-secondary-600 dark:text-secondary-400'
                  )}
                  aria-current={isLast || item.current ? 'page' : undefined}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

export default Breadcrumb;
