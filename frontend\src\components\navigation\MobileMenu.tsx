import { useState } from 'react';
import { cn } from '../../utils/cn';
import { useApp } from '../../contexts/AppContext';
import ThemeToggle from '../ui/ThemeToggle';

interface MobileMenuProps {
  className?: string;
}

export function MobileMenu({ className }: MobileMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { state, logout } = useApp();

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  return (
    <div className={cn('md:hidden', className)}>
      {/* Mobile menu button */}
      <button
        onClick={toggleMenu}
        className="inline-flex items-center justify-center p-2 rounded-lg text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-secondary-100 hover:bg-secondary-100 dark:hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
        aria-expanded={isOpen}
        aria-label="Toggle mobile menu"
      >
        <svg
          className={cn('w-6 h-6 transition-transform duration-200', isOpen && 'rotate-90')}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isOpen ? (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          ) : (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          )}
        </svg>
      </button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm animate-fade-in"
            onClick={closeMenu}
          />
          
          {/* Menu panel */}
          <div className="fixed top-0 right-0 h-full w-80 max-w-sm bg-white dark:bg-secondary-800 shadow-xl animate-slide-down">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h2 className="text-lg font-bold font-display gradient-text">OrderFlow</h2>
                </div>
                <button
                  onClick={closeMenu}
                  className="p-2 text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* User info */}
              <div className="p-6 border-b border-secondary-200 dark:border-secondary-700">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white text-lg font-semibold">
                    {(state.user?.firstName?.[0] || state.user?.username?.[0] || 'U').toUpperCase()}
                  </div>
                  <div>
                    <p className="font-semibold text-secondary-900 dark:text-secondary-100">
                      {state.user?.firstName || state.user?.username}
                    </p>
                    <p className="text-sm text-secondary-500 dark:text-secondary-400">
                      {state.user?.email}
                    </p>
                  </div>
                </div>
              </div>

              {/* Navigation items */}
              <div className="flex-1 p-6 space-y-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                    Navigation
                  </h3>
                  <nav className="space-y-1">
                    <a
                      href="#dashboard"
                      className="flex items-center px-3 py-2 text-sm font-medium text-secondary-900 dark:text-secondary-100 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-lg"
                      onClick={closeMenu}
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Dashboard
                    </a>
                    <a
                      href="#orders"
                      className="flex items-center px-3 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-700 rounded-lg transition-colors"
                      onClick={closeMenu}
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                      Orders
                    </a>
                    <a
                      href="#analytics"
                      className="flex items-center px-3 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-700 rounded-lg transition-colors"
                      onClick={closeMenu}
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      Analytics
                    </a>
                  </nav>
                </div>

                {/* Settings */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                    Settings
                  </h3>
                  <div className="flex items-center justify-between px-3 py-2">
                    <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300">Theme</span>
                    <ThemeToggle size="sm" />
                  </div>
                </div>

                {/* Connection Status */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                    Status
                  </h3>
                  <div className="flex items-center px-3 py-2">
                    <div className={`w-2 h-2 rounded-full mr-3 ${
                      state.connectionStatus.connected ? 'bg-success-500 animate-pulse' : 'bg-error-500'
                    }`}></div>
                    <span className="text-sm text-secondary-700 dark:text-secondary-300">
                      {state.connectionStatus.connected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="p-6 border-t border-secondary-200 dark:border-secondary-700">
                <button
                  onClick={() => {
                    logout();
                    closeMenu();
                  }}
                  className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-error-700 dark:text-error-400 hover:bg-error-50 dark:hover:bg-error-900/20 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MobileMenu;
