import { useState, useEffect } from 'react';
import { useApp } from '../../contexts/AppContext';
import OrderService from '../../services/orderService';
import EventService from '../../services/eventService';
import AnimatedCounter from '../ui/AnimatedCounter';
import type { Order, Event } from '../../services/api';

export function Dashboard() {
  const { state } = useApp();
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [recentEvents, setRecentEvents] = useState<Event[]>([]);
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalSpent: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Load recent orders
      const ordersResponse = await OrderService.getMyOrders({ limit: 5 });
      setRecentOrders(ordersResponse.data.orders);

      // Calculate user stats
      const allOrdersResponse = await OrderService.getMyOrders({ limit: 100 });
      const allOrders = allOrdersResponse.data.orders;
      
      const userStats = {
        totalOrders: allOrders.length,
        pendingOrders: allOrders.filter(order => ['pending', 'confirmed', 'processing'].includes(order.status)).length,
        completedOrders: allOrders.filter(order => order.status === 'delivered').length,
        totalSpent: allOrders
          .filter(order => order.status !== 'cancelled')
          .reduce((total, order) => total + order.totalAmount, 0),
      };
      setStats(userStats);

      // Load recent events
      const eventsResponse = await EventService.getAllEvents({ limit: 10 });
      setRecentEvents(eventsResponse.data.events);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'badge-warning',
      confirmed: 'badge-primary',
      processing: 'badge-secondary',
      shipped: 'badge-primary',
      delivered: 'badge-success',
      cancelled: 'badge-error',
      refunded: 'badge-secondary'
    };
    return colors[status as keyof typeof colors] || 'badge-secondary';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="card p-6 animate-pulse">
          <div className="h-8 bg-secondary-200 rounded-lg w-1/3 mb-4"></div>
          <div className="h-4 bg-secondary-200 rounded w-2/3"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-secondary-200 rounded-md"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-secondary-200 rounded w-1/2 mb-2"></div>
                  <div className="h-6 bg-secondary-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Section */}
      <div className="card-glass p-8 animate-fade-in">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold font-display gradient-text mb-2">
              Welcome back, {state.user?.firstName || state.user?.username}! 👋
            </h1>
            <p className="text-lg text-secondary-600 dark:text-secondary-400">
              Here's your order management overview for today.
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-br from-primary-400 to-primary-600 rounded-2xl flex items-center justify-center float animate-pulse-soft">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Orders Card */}
        <div className="card hover-lift hover-scale group animate-slide-up" style={{ animationDelay: '0.1s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400 mb-1">Total Orders</p>
                <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100 group-hover:text-primary-600 transition-colors">
                  <AnimatedCounter value={stats.totalOrders} duration={1500} />
                </p>
                <div className="flex items-center mt-2">
                  <span className="text-xs text-success-600 bg-success-100 px-2 py-1 rounded-full">
                    +12% from last month
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-xl flex items-center justify-center group-hover:shadow-glow transition-all duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Orders Card */}
        <div className="card hover-lift hover-scale group animate-slide-up" style={{ animationDelay: '0.2s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400 mb-1">Pending Orders</p>
                <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100 group-hover:text-warning-600 transition-colors">
                  <AnimatedCounter value={stats.pendingOrders} duration={1500} />
                </p>
                <div className="flex items-center mt-2">
                  <span className="text-xs text-warning-600 bg-warning-100 px-2 py-1 rounded-full">
                    Needs attention
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-warning-400 to-warning-600 rounded-xl flex items-center justify-center group-hover:shadow-glow transition-all duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Completed Orders Card */}
        <div className="card hover-lift hover-scale group animate-slide-up" style={{ animationDelay: '0.3s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400 mb-1">Completed Orders</p>
                <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100 group-hover:text-success-600 transition-colors">
                  <AnimatedCounter value={stats.completedOrders} duration={1500} />
                </p>
                <div className="flex items-center mt-2">
                  <span className="text-xs text-success-600 bg-success-100 px-2 py-1 rounded-full">
                    +8% this week
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-success-400 to-success-600 rounded-xl flex items-center justify-center group-hover:shadow-glow transition-all duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Total Revenue Card */}
        <div className="card hover-lift hover-scale group animate-slide-up" style={{ animationDelay: '0.4s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400 mb-1">Total Revenue</p>
                <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100 group-hover:text-purple-600 transition-colors">
                  <AnimatedCounter
                    value={stats.totalSpent}
                    duration={2000}
                    prefix="$"
                    decimals={2}
                    separator=","
                  />
                </p>
                <div className="flex items-center mt-2">
                  <span className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                    +15% growth
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center group-hover:shadow-glow transition-all duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Recent Orders and Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="card animate-slide-up" style={{ animationDelay: '0.5s' }}>
          <div className="px-6 py-4 border-b border-secondary-200 dark:border-secondary-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100">Recent Orders</h3>
              <button className="btn-ghost text-sm px-3 py-1">
                View All
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
          <div className="p-6">
            {recentOrders.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <p className="text-secondary-500 dark:text-secondary-400">No orders yet</p>
                <p className="text-sm text-secondary-400 dark:text-secondary-500 mt-1">Your recent orders will appear here</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentOrders.map((order, index) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between p-4 border border-secondary-200 dark:border-secondary-700 rounded-xl hover:shadow-md hover-lift transition-all duration-200 group"
                    style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-semibold text-secondary-900 dark:text-secondary-100 group-hover:text-primary-600 transition-colors">
                          #{order.orderNumber}
                        </p>
                        <p className="text-sm text-secondary-500 dark:text-secondary-400">{formatDate(order.createdAt)}</p>
                        <p className="text-sm font-medium text-secondary-700 dark:text-secondary-300">{formatCurrency(order.totalAmount)}</p>
                      </div>
                    </div>
                    <span className={`badge ${getStatusColor(order.status)} capitalize`}>
                      {order.status}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Events */}
        <div className="card animate-slide-up" style={{ animationDelay: '0.6s' }}>
          <div className="px-6 py-4 border-b border-secondary-200 dark:border-secondary-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100">Recent Activity</h3>
              <button className="btn-ghost text-sm px-3 py-1">
                View All
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
          <div className="p-6">
            {recentEvents.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <p className="text-secondary-500 dark:text-secondary-400">No recent activity</p>
                <p className="text-sm text-secondary-400 dark:text-secondary-500 mt-1">System events will appear here</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentEvents.slice(0, 5).map((event, index) => (
                  <div
                    key={event.id}
                    className="flex items-start space-x-4 p-3 rounded-lg hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors duration-200 animate-fade-in"
                    style={{ animationDelay: `${0.7 + index * 0.1}s` }}
                  >
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-secondary-900 dark:text-secondary-100">
                        {event.type.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                        {formatDate(event.metadata.timestamp)}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      <span className="w-2 h-2 bg-primary-500 rounded-full block animate-pulse"></span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Connection Status */}
      <div className="card animate-slide-up" style={{ animationDelay: '0.7s' }}>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                state.connectionStatus.connected
                  ? 'bg-gradient-to-br from-success-400 to-success-600'
                  : 'bg-gradient-to-br from-error-400 to-error-600'
              }`}>
                {state.connectionStatus.connected ? (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                  </svg>
                ) : (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0 0L5.636 18.364m12.728 0L5.636 5.636" />
                  </svg>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-secondary-900 dark:text-secondary-100">
                  Real-time Connection
                </h3>
                <p className="text-sm text-secondary-600 dark:text-secondary-400">
                  {state.connectionStatus.connected
                    ? 'Connected - Receiving live updates'
                    : 'Disconnected - Updates may be delayed'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full ${
                state.connectionStatus.connected
                  ? 'bg-success-100 text-success-700'
                  : 'bg-error-100 text-error-700'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  state.connectionStatus.connected
                    ? 'bg-success-500 animate-pulse'
                    : 'bg-error-500'
                }`}></div>
                <span className="text-xs font-medium">
                  {state.connectionStatus.connected ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
