import React from 'react';
import { cn } from '../../utils/cn';

interface LoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
  text?: string;
}

export function Loading({
  variant = 'spinner',
  size = 'md',
  color = 'primary',
  className,
  text,
}: LoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-primary-600',
    secondary: 'text-secondary-600',
    white: 'text-white',
  };

  if (variant === 'spinner') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center space-y-2">
          <div className={cn(
            'animate-spin rounded-full border-2 border-current border-t-transparent',
            sizeClasses[size],
            colorClasses[color]
          )} />
          {text && (
            <p className={cn('text-sm', colorClasses[color])}>
              {text}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={cn('flex items-center justify-center space-x-1', className)}>
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                'rounded-full animate-pulse',
                size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : size === 'lg' ? 'w-4 h-4' : 'w-5 h-5',
                colorClasses[color] === 'text-white' ? 'bg-white' : 
                colorClasses[color] === 'text-primary-600' ? 'bg-primary-600' : 'bg-secondary-600'
              )}
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
        {text && (
          <p className={cn('text-sm ml-3', colorClasses[color])}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center space-y-2">
          <div className={cn(
            'rounded-full animate-pulse',
            sizeClasses[size],
            colorClasses[color] === 'text-white' ? 'bg-white' : 
            colorClasses[color] === 'text-primary-600' ? 'bg-primary-600' : 'bg-secondary-600'
          )} />
          {text && (
            <p className={cn('text-sm', colorClasses[color])}>
              {text}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'skeleton') {
    return (
      <div className={cn('animate-pulse space-y-3', className)}>
        <div className="h-4 bg-secondary-200 dark:bg-secondary-700 rounded w-3/4"></div>
        <div className="h-4 bg-secondary-200 dark:bg-secondary-700 rounded w-1/2"></div>
        <div className="h-4 bg-secondary-200 dark:bg-secondary-700 rounded w-5/6"></div>
      </div>
    );
  }

  return null;
}

interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}

export function LoadingOverlay({
  isLoading,
  children,
  loadingText = 'Loading...',
  className,
}: LoadingOverlayProps) {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-secondary-900/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg">
          <Loading variant="spinner" size="lg" text={loadingText} />
        </div>
      )}
    </div>
  );
}

export default Loading;
