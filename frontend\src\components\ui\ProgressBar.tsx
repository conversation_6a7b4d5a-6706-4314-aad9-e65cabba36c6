import { useEffect, useState, useRef } from 'react';
import { cn } from '../../utils/cn';

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
  striped?: boolean;
  duration?: number;
}

export function ProgressBar({
  value,
  max = 100,
  className,
  size = 'md',
  variant = 'primary',
  showLabel = false,
  label,
  animated = true,
  striped = false,
  duration = 1000,
}: ProgressBarProps) {
  const [currentValue, setCurrentValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  const percentage = Math.min((value / max) * 100, 100);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          animateProgress();
        }
      },
      { threshold: 0.1 }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [percentage, isVisible]);

  const animateProgress = () => {
    if (!animated) {
      setCurrentValue(percentage);
      return;
    }

    const startTime = Date.now();
    const startValue = 0;
    const endValue = percentage;

    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      
      const newValue = startValue + (endValue - startValue) * easeOut;
      setCurrentValue(newValue);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setCurrentValue(endValue);
      }
    };

    requestAnimationFrame(animate);
  };

  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
  };

  const variantClasses = {
    primary: 'bg-primary-500',
    secondary: 'bg-secondary-500',
    success: 'bg-success-500',
    warning: 'bg-warning-500',
    error: 'bg-error-500',
  };

  return (
    <div className={cn('w-full', className)}>
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300">
            {label || `${Math.round(currentValue)}%`}
          </span>
          {showLabel && !label && (
            <span className="text-sm text-secondary-500 dark:text-secondary-400">
              {value}/{max}
            </span>
          )}
        </div>
      )}
      
      <div
        ref={elementRef}
        className={cn(
          'w-full bg-secondary-200 dark:bg-secondary-700 rounded-full overflow-hidden',
          sizeClasses[size]
        )}
      >
        <div
          className={cn(
            'h-full rounded-full transition-all duration-300 ease-out',
            variantClasses[variant],
            striped && 'bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:20px_100%] animate-pulse'
          )}
          style={{
            width: `${currentValue}%`,
            transition: animated ? `width ${duration}ms ease-out` : 'none',
          }}
        >
          {striped && (
            <div className="h-full w-full bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
          )}
        </div>
      </div>
    </div>
  );
}

export default ProgressBar;
