import { useState } from 'react';
import { useApp } from '../../contexts/AppContext';

interface LoginFormProps {
  onSuccess?: () => void;
  onSwitchToRegister?: () => void;
}

export function LoginForm({ onSuccess, onSwitchToRegister }: LoginFormProps) {
  const { login, state } = useApp();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await login(formData.email, formData.password);
      onSuccess?.();
    } catch (error) {
      // Error is handled in the context
      console.error('Login failed:', error);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      {/* Glass morphism card */}
      <div className="card-glass p-8 animate-fade-in">
        {/* Header with gradient text */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold font-display gradient-text mb-2">
            Welcome Back
          </h2>
          <p className="text-secondary-600 dark:text-secondary-400">
            Sign in to your account to continue
          </p>
        </div>

        {/* Error message with improved styling */}
        {state.error && (
          <div className="mb-6 p-4 bg-error-50 border border-error-200 text-error-700 rounded-xl animate-slide-down">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2 text-error-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {state.error}
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email field with floating label effect */}
          <div className="relative">
            <label htmlFor="email" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
              Email Address
            </label>
            <div className="relative">
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`input ${errors.email ? 'input-error' : ''} pl-10 transition-all duration-200 hover:shadow-md focus:shadow-lg`}
                placeholder="Enter your email"
                disabled={state.isLoading}
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
            {errors.email && (
              <p className="mt-2 text-sm text-error-600 animate-slide-down">{errors.email}</p>
            )}
          </div>

          {/* Password field with show/hide toggle */}
          <div className="relative">
            <label htmlFor="password" className="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`input ${errors.password ? 'input-error' : ''} pl-10 transition-all duration-200 hover:shadow-md focus:shadow-lg`}
                placeholder="Enter your password"
                disabled={state.isLoading}
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            {errors.password && (
              <p className="mt-2 text-sm text-error-600 animate-slide-down">{errors.password}</p>
            )}
          </div>

          {/* Enhanced submit button */}
          <button
            type="submit"
            disabled={state.isLoading}
            className="btn-primary w-full py-3 text-base font-semibold hover-lift disabled:transform-none relative overflow-hidden group"
          >
            {state.isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-3"></div>
                Signing In...
              </div>
            ) : (
              <>
                <span className="relative z-10">Sign In</span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </>
            )}
          </button>
        </form>

        {/* Footer links with improved styling */}
        <div className="mt-8 space-y-4">
          <div className="text-center">
            <p className="text-sm text-secondary-600 dark:text-secondary-400">
              Don't have an account?{' '}
              <button
                type="button"
                onClick={onSwitchToRegister}
                className="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200 hover:underline"
              >
                Sign up
              </button>
            </p>
          </div>

          <div className="text-center">
            <button
              type="button"
              className="text-sm text-secondary-500 hover:text-primary-600 transition-colors duration-200 hover:underline"
              onClick={() => {
                // TODO: Implement forgot password
                alert('Forgot password functionality not implemented yet');
              }}
            >
              Forgot your password?
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoginForm;
