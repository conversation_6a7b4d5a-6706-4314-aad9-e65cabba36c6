import { useEffect, useRef } from 'react';

// Add this type for LayoutShift if not available in the DOM lib
// See: https://web.dev/articles/cls
interface LayoutShift extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

export function usePerformance(componentName: string, enabled: boolean = process.env.NODE_ENV === 'development') {
  const startTimeRef = useRef<number>(undefined);
  const mountTimeRef = useRef<number>(undefined);

  useEffect(() => {
    if (!enabled) return;

    startTimeRef.current = performance.now();
    
    return () => {
      if (startTimeRef.current) {
        const renderTime = performance.now() - startTimeRef.current;
        console.log(`🚀 ${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
    };
  });

  useEffect(() => {
    if (!enabled) return;

    mountTimeRef.current = performance.now();
    
    return () => {
      if (mountTimeRef.current) {
        const mountTime = performance.now() - mountTimeRef.current;
        console.log(`⚡ ${componentName} mount time: ${mountTime.toFixed(2)}ms`);
      }
    };
  }, [componentName, enabled]);
}

export function measurePerformance<T extends (...args: unknown[]) => unknown>(
  fn: T,
  name: string
): T {
  return ((...args: Parameters<T>) => {
    const start = performance.now();
    const result = fn(...args);
    const end = performance.now();
    
    console.log(`⏱️ ${name} execution time: ${(end - start).toFixed(2)}ms`);
    
    return result;
  }) as T;
}

export function useWebVitals() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Measure First Contentful Paint (FCP)
    const observer = new PerformanceObserver((list: PerformanceObserverEntryList) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
          console.log(`🎨 First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
        }
      }
    });

    observer.observe({ entryTypes: ['paint'] });

    // Measure Largest Contentful Paint (LCP)
    const lcpObserver = new PerformanceObserver((list: PerformanceObserverEntryList) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        console.log(`🖼️ Largest Contentful Paint: ${lastEntry.startTime.toFixed(2)}ms`);
      }
    });

    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // Measure Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list: PerformanceObserverEntryList) => {
      for (const entry of list.getEntries()) {
        const layoutShiftEntry = entry as LayoutShift;
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value;
        }
      }
      console.log(`📐 Cumulative Layout Shift: ${clsValue.toFixed(4)}`);
    });

    clsObserver.observe({ entryTypes: ['layout-shift'] });

    return () => {
      observer.disconnect();
      lcpObserver.disconnect();
      clsObserver.disconnect();
    };
  }, []);
}

export function useMemoryUsage() {
  useEffect(() => {
    if (typeof window === 'undefined' || !(performance as unknown as { memory?: object }).memory) return;

    const logMemoryUsage = () => {
      const memory = (performance as unknown as { memory: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
      console.log(`💾 Memory Usage:`, {
        used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
      });
    };

    const interval = setInterval(logMemoryUsage, 10000); // Log every 10 seconds

    return () => clearInterval(interval);
  }, []);
}
