import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/cn';

interface SearchInputProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onClear?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showSearchIcon?: boolean;
  showClearButton?: boolean;
  autoFocus?: boolean;
  debounceMs?: number;
}

export function SearchInput({
  placeholder = 'Search...',
  onSearch,
  onClear,
  className,
  size = 'md',
  showSearchIcon = true,
  showClearButton = true,
  autoFocus = false,
  debounceMs = 300,
}: SearchInputProps) {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(query);
      }
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, onSearch, debounceMs]);

  const handleClear = () => {
    setQuery('');
    if (onClear) {
      onClear();
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClear();
    }
  };

  const sizeClasses = {
    sm: 'h-8 text-sm',
    md: 'h-10 text-sm',
    lg: 'h-12 text-base',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const paddingClasses = {
    sm: showSearchIcon ? 'pl-8 pr-8' : 'px-3',
    md: showSearchIcon ? 'pl-10 pr-10' : 'px-4',
    lg: showSearchIcon ? 'pl-12 pr-12' : 'px-5',
  };

  return (
    <div className={cn('relative', className)}>
      {/* Search Icon */}
      {showSearchIcon && (
        <div className={cn(
          'absolute left-0 top-0 h-full flex items-center justify-center pointer-events-none',
          size === 'sm' ? 'w-8' : size === 'md' ? 'w-10' : 'w-12'
        )}>
          <svg 
            className={cn(
              'text-secondary-400 transition-colors',
              iconSizeClasses[size],
              isFocused && 'text-primary-500'
            )} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      )}

      {/* Input */}
      <input
        ref={inputRef}
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={cn(
          'w-full border border-secondary-300 dark:border-secondary-600 rounded-lg',
          'bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100',
          'placeholder-secondary-400 dark:placeholder-secondary-500',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
          'transition-all duration-200',
          sizeClasses[size],
          paddingClasses[size]
        )}
      />

      {/* Clear Button */}
      {showClearButton && query && (
        <button
          onClick={handleClear}
          className={cn(
            'absolute right-0 top-0 h-full flex items-center justify-center',
            'text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300',
            'transition-colors duration-200',
            size === 'sm' ? 'w-8' : size === 'md' ? 'w-10' : 'w-12'
          )}
          type="button"
          aria-label="Clear search"
        >
          <svg className={iconSizeClasses[size]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
}

export default SearchInput;
